<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #ffffff;
}

.main-title {
    font-size: 2.5em;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1.5em;
    color: #2c3e50;
    border-bottom: 3px solid #34495e;
    padding-bottom: 0.5em;
}

.section-title {
    font-size: 1.8em;
    font-weight: 600;
    margin-top: 2em;
    margin-bottom: 1em;
    color: #2c3e50;
    border-bottom: 2px solid #7f8c8d;
    padding-bottom: 0.3em;
}

.subsection-title {
    font-size: 1.4em;
    font-weight: 500;
    margin-top: 1.5em;
    margin-bottom: 0.8em;
    color: #34495e;
}

.feature-title {
    font-size: 1.2em;
    font-weight: 500;
    margin-top: 1.2em;
    margin-bottom: 0.6em;
    color: #34495e;
}

.feature-item {
    margin-bottom: 0.5em;
    padding-left: 1em;
    color: #2c3e50;
}

.feature-label {
    font-weight: 600;
    color: #2c3e50;
}

.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 1em;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    margin: 1em 0;
    color: #2c3e50;
}

.document-info {
    border-top: 2px solid #7f8c8d;
    padding-top: 1em;
    margin-top: 2em;
    font-size: 0.9em;
    color: #7f8c8d;
}
</style>

<div class="main-title">Αναφορά Ασφάλειας - Εφαρμογή BioCleaning</div>

<div class="section-title">Εκτελεστική Περίληψη</div>

Το παρόν έγγραφο παρέχει μια ολοκληρωμένη επισκόπηση των μέτρων ασφαλείας που έχουν υλοποιηθεί στην εφαρμογή BioCleaning. Η εφαρμογή έχει σχεδιαστεί με χαρακτηριστικά ασφαλείας enterprise επιπέδου που παρέχουν ισχυρή προστασία και αξιοπιστία.

<div class="section-title">Επισκόπηση Αρχιτεκτονικής Ασφαλείας</div>

Η εφαρμογή υλοποιεί ένα προηγμένο σύστημα ασφαλείας που παρέχει πλήρη προστασία enterprise επιπέδου με αυστηρούς ελέγχους ασφαλείας.

<div class="section-title">Υλοποιημένα Χαρακτηριστικά Ασφαλείας</div>

<div class="subsection-title">1. Πιστοποίηση & Εξουσιοδότηση</div>

<div class="feature-title">Σύστημα Διαχείρισης Χρηστών</div>
<div class="feature-item"><span class="feature-label">Βιομηχανικό Πρότυπο:</span> Βασισμένο σε ώριμο σύστημα διαχείρισης χρηστών</div>
<div class="feature-item"><span class="feature-label">JWT Tokens:</span> Ασφαλής υλοποίηση JSON Web Token με 24 ώρες διάρκεια ζωής για ενισχυμένη ασφάλεια και βολικότητα χρήστη</div>
<div class="feature-item"><span class="feature-label">Email Verification:</span> Υποχρεωτική επαλήθευση email πριν την ενεργοποίηση λογαριασμού</div>
<div class="feature-item"><span class="feature-label">Password Reset:</span> Ασφαλής διαδικασία επαναφοράς κωδικού με χρονικά περιορισμένα tokens</div>

<div class="feature-title">Cookie Security</div>
<div class="feature-item"><span class="feature-label">Ασφαλής Διαμόρφωση:</span> secure=True (μόνο HTTPS), SameSite=strict</div>
<div class="feature-item"><span class="feature-label">HttpOnly Cookies:</span> Αποτρέπει XSS επιθέσεις μπλοκάροντας την πρόσβαση JavaScript</div>
<div class="feature-item"><span class="feature-label">Ασφαλής Μετάδοση:</span> Cookies κρυπτογραφημένα και υπογεγραμμένα με μυστικά κλειδιά</div>

<div class="subsection-title">2. Επικύρωση & Καθαρισμός Δεδομένων Εισόδου</div>

<div class="feature-title">Ολοκληρωμένη Server-Side Validation</div>
<div class="feature-item"><span class="feature-label">Email Validation:</span></div>
<div class="feature-item" style="margin-left: 1em;">Επικύρωση μορφής με regex patterns</div>
<div class="feature-item" style="margin-left: 1em;">Έλεγχος ύπαρξης domain</div>
<div class="feature-item" style="margin-left: 1em;">Ανίχνευση και αποκλεισμός disposable email</div>
<div class="feature-item" style="margin-left: 1em;">Προστασία κατά email injection επιθέσεων</div>

<div class="feature-title">Password Security</div>
<div class="feature-item"><span class="feature-label">Απαιτήσεις Ισχύος:</span></div>
<div class="feature-item" style="margin-left: 1em;">Ελάχιστο 8 χαρακτήρες, μέγιστο 128 χαρακτήρες</div>
<div class="feature-item" style="margin-left: 1em;">Πρέπει να περιέχει κεφαλαία, πεζά, ψηφία και ειδικούς χαρακτήρες</div>
<div class="feature-item" style="margin-left: 1em;">Ανίχνευση προβλέψιμων patterns για αποτροπή αδύναμων κωδικών</div>
<div class="feature-item" style="margin-left: 1em;">Έλεγχος κατά λίστας συνηθισμένων κωδικών (error message: "Password is too common")</div>
<div class="feature-item"><span class="feature-label">Ασφαλής Αποθήκευση:</span> Κωδικοί κατακερματισμένοι με αλγορίθμους enterprise προτύπου</div>

<div class="feature-title">Input Sanitization</div>
<div class="feature-item"><span class="feature-label">XSS Prevention:</span> HTML escaping όλων των user inputs</div>
<div class="feature-item"><span class="feature-label">SQL Injection Protection:</span> Parameterized queries μέσω SQLAlchemy ORM</div>
<div class="feature-item"><span class="feature-label">Control Character Removal:</span> Φιλτράρισμα null bytes και control characters</div>
<div class="feature-item"><span class="feature-label">Length Limits:</span> Επιβολή μέγιστων μηκών σε όλα τα πεδία εισόδου</div>

<div class="subsection-title">3. Cross-Site Request Forgery (CSRF) Protection</div>

<div class="feature-title">CSRF Protection</div>
<div class="feature-item"><span class="feature-label">Πλήρη CSRF token validation</span> σε όλες τις φόρμες</div>
<div class="feature-item"><span class="feature-label">Token Management:</span> Ασφαλής δημιουργία token και session-based validation</div>
<div class="feature-item"><span class="feature-label">Form Integration:</span> Αυτόματη injection token σε όλες τις πιστοποιημένες φόρμες</div>

<div class="subsection-title">4. Rate Limiting & DDoS Protection</div>

<div class="feature-title">Έξυπνο Rate Limiting</div>
<div class="feature-item"><span class="feature-label">Όρια Παραγωγής:</span></div>
<div class="feature-item" style="margin-left: 1em;">Authentication endpoints: 5 προσπάθειες ανά λεπτό</div>
<div class="feature-item" style="margin-left: 1em;">Email endpoints: 3 προσπάθειες ανά λεπτό</div>
<div class="feature-item" style="margin-left: 1em;">General API: 100 αιτήματα ανά λεπτό</div>
<div class="feature-item"><span class="feature-label">IP-Based Tracking:</span> Rate limiting ανά IP address</div>
<div class="feature-item"><span class="feature-label">Automatic Blocking:</span> Προσωρινός αποκλεισμός καταχρηστικών IPs</div>

<div class="subsection-title">5. Security Headers</div>

<div class="feature-title">Security Headers Παραγωγής</div>
<div class="feature-item"><span class="feature-label">X-Content-Type-Options:</span> nosniff - Αποτρέπει MIME type sniffing</div>
<div class="feature-item"><span class="feature-label">X-Frame-Options:</span> DENY - Αποτρέπει clickjacking επιθέσεις</div>
<div class="feature-item"><span class="feature-label">X-XSS-Protection:</span> 1; mode=block - Browser XSS protection</div>
<div class="feature-item"><span class="feature-label">Strict-Transport-Security:</span> Επιβάλλει HTTPS συνδέσεις</div>
<div class="feature-item"><span class="feature-label">Content-Security-Policy:</span> Περιορίζει τις πηγές φόρτωσης πόρων</div>
<div class="feature-item"><span class="feature-label">Referrer-Policy:</span> Ελέγχει τη διαρροή referrer πληροφοριών</div>

<div class="subsection-title">6. Προστασία Δεδομένων</div>

<div class="feature-title">Environment Variables</div>
<div class="feature-item"><span class="feature-label">Secret Management:</span> Όλα τα ευαίσθητα δεδομένα αποθηκεύονται σε environment variables</div>
<div class="feature-item"><span class="feature-label">Χωρίς Hardcoded Secrets:</span> Κωδικοί, κλειδιά και tokens εξωτερικευμένα</div>
<div class="feature-item"><span class="feature-label">Git Protection:</span> Αρχεία .env εξαιρούνται από version control</div>
<div class="feature-item"><span class="feature-label">Production Deployment:</span> Ασφαλής διαχείριση environment variables στον Deployment Server</div>

<div class="feature-title">Database Security</div>
<div class="feature-item"><span class="feature-label">SQLite Security:</span> File-based βάση δεδομένων με κατάλληλα δικαιώματα</div>
<div class="feature-item"><span class="feature-label">Connection Security:</span> Ασφαλείς συνδέσεις βάσης δεδομένων με error handling</div>
<div class="feature-item"><span class="feature-label">Data Isolation:</span> Δεδομένα χρηστών απομονωμένα μέσω application-level ελέγχων</div>

<div class="subsection-title">7. Security Logging & Monitoring</div>

<div class="feature-title">Ολοκληρωμένο Logging</div>
<div class="feature-item"><span class="feature-label">Authentication Events:</span> Όλες οι προσπάθειες login καταγράφονται με IP και user agent</div>
<div class="feature-item"><span class="feature-label">Security Events:</span> CSRF παραβιάσεις, rate limit παραβάσεις, ύποπτη δραστηριότητα</div>
<div class="feature-item"><span class="feature-label">Structured Logging:</span> JSON format για παραγωγή με αυτόματη περιστροφή αρχείων</div>
<div class="feature-item"><span class="feature-label">Log Rotation:</span> Αυτόματη περιστροφή αρχείων log για αποφυγή προβλημάτων χώρου</div>

<div class="feature-title">Real-Time Monitoring</div>
<div class="feature-item"><span class="feature-label">Failed Login Tracking:</span> Αυτόματη ανίχνευση brute force προσπαθειών</div>
<div class="feature-item"><span class="feature-label">Suspicious IP Detection:</span> Αυτόματη σήμανση προβληματικών IP addresses</div>
<div class="feature-item"><span class="feature-label">Rate Limit Monitoring:</span> Παρακολούθηση και ειδοποίηση για rate limit παραβιάσεις</div>
<div class="feature-item"><span class="feature-label">Security Event Correlation:</span> Ανίχνευση patterns σε security events</div>

<div class="subsection-title">8. Transport Security</div>

<div class="feature-title">HTTPS Enforcement</div>
<div class="feature-item"><span class="feature-label">Αυτόματο HTTPS redirect middleware</span></div>
<div class="feature-item"><span class="feature-label">Certificate Management:</span> Διαχείριση από την πλατφόρμα Deployment Server</div>
<div class="feature-item"><span class="feature-label">Secure Cookie Transmission:</span> Cookies αποστέλλονται μόνο μέσω κρυπτογραφημένων συνδέσεων</div>

<div class="section-title">Διαμόρφωση Ασφαλείας</div>

<div class="subsection-title">Environment Variables Παραγωγής</div>
<div class="code-block">
ENVIRONMENT=production
BASE_URL=https://your-app.onrender.com
MAIL_PASSWORD=your-gmail-app-password
SECRET=your-secret-key
</div>

<div class="section-title">Συμμόρφωση & Πρότυπα</div>

<div class="subsection-title">Συμμόρφωση με Βιομηχανικά Πρότυπα</div>
<div class="feature-item"><span class="feature-label">OWASP Top 10:</span> Προστασία κατά όλων των OWASP Top 10 vulnerabilities</div>
<div class="feature-item"><span class="feature-label">NIST Guidelines:</span> Ακολουθία των συστάσεων του NIST cybersecurity framework</div>
<div class="feature-item"><span class="feature-label">RFC Standards:</span> Συμμόρφωση με σχετικά RFC πρότυπα για web security</div>

<div class="subsection-title">Βέλτιστες Πρακτικές Ασφαλείας</div>
<div class="feature-item"><span class="feature-label">Defense in Depth:</span> Πολλαπλά επίπεδα ελέγχων ασφαλείας</div>
<div class="feature-item"><span class="feature-label">Principle of Least Privilege:</span> Υλοποίηση ελάχιστων δικαιωμάτων πρόσβασης</div>
<div class="feature-item"><span class="feature-label">Secure by Default:</span> Χαρακτηριστικά ασφαλείας ενεργοποιημένα από προεπιλογή</div>
<div class="feature-item"><span class="feature-label">Regular Updates:</span> Ενημερώσεις framework και dependencies για security patches</div>

<div class="section-title">Ασφάλεια Deployment</div>

<div class="subsection-title">Ασφάλεια Πλατφόρμας Deployment Server</div>
<div class="feature-item"><span class="feature-label">Infrastructure Security:</span> Αξιοποίηση της ασφαλούς υποδομής του Deployment Server</div>
<div class="feature-item"><span class="feature-label">Automatic HTTPS:</span> SSL/TLS πιστοποιητικά διαχειρίζονται αυτόματα</div>
<div class="feature-item"><span class="feature-label">Environment Isolation:</span> Ασφαλής διαχείριση environment variables</div>
<div class="feature-item"><span class="feature-label">Network Security:</span> Προστατευμένη δικτυακή υποδομή</div>

<div class="subsection-title">Βέλτιστες Πρακτικές Deployment</div>
<div class="feature-item"><span class="feature-label">Secure Deployment Pipeline:</span> Αυτοματοποιημένο deployment με ελέγχους ασφαλείας</div>
<div class="feature-item"><span class="feature-label">Configuration Management:</span> Ασφαλείς πρακτικές διαχείρισης διαμόρφωσης</div>

<div class="section-title">Παρακολούθηση & Αντίδραση σε Περιστατικά</div>

<div class="subsection-title">Security Monitoring</div>
<div class="feature-item"><span class="feature-label">Real-Time Alerts:</span> Άμεση ειδοποίηση για security events</div>
<div class="feature-item"><span class="feature-label">Log Analysis:</span> Ολοκληρωμένη ανάλυση logs για ανίχνευση απειλών</div>
<div class="feature-item"><span class="feature-label">Performance Monitoring:</span> Παρακολούθηση απόδοσης χαρακτηριστικών ασφαλείας</div>
<div class="feature-item"><span class="feature-label">Compliance Monitoring:</span> Συνεχής επαλήθευση συμμόρφωσης</div>

<div class="subsection-title">Incident Response</div>
<div class="feature-item"><span class="feature-label">Automated Response:</span> Αυτόματος αποκλεισμός ύποπτων δραστηριοτήτων</div>
<div class="feature-item"><span class="feature-label">Logging:</span> Ολοκληρωμένο audit trail για διερεύνηση περιστατικών</div>
<div class="feature-item"><span class="feature-label">Recovery Procedures:</span> Τεκμηριωμένες διαδικασίες για αποκατάσταση από security incidents</div>

<div class="section-title">Συστάσεις για Συνεχή Ασφάλεια</div>

<div class="subsection-title">Τακτικές Πρακτικές Ασφαλείας</div>
<div class="feature-item">1. <span class="feature-label">Dependency Updates:</span> Τακτικές ενημερώσεις όλων των dependencies</div>
<div class="feature-item">2. <span class="feature-label">Security Audits:</span> Περιοδικές αξιολογήσεις ασφαλείας</div>
<div class="feature-item">3. <span class="feature-label">Log Review:</span> Τακτική επισκόπηση των security logs</div>
<div class="feature-item">4. <span class="feature-label">Configuration Review:</span> Περιοδική επισκόπηση των διαμορφώσεων ασφαλείας</div>

<div class="subsection-title">Μελλοντικές Βελτιώσεις</div>
<div class="feature-item">1. <span class="feature-label">Two-Factor Authentication:</span> Υλοποίηση 2FA για ενισχυμένη ασφάλεια</div>
<div class="feature-item">2. <span class="feature-label">Advanced Threat Detection:</span> Ανίχνευση απειλών βασισμένη σε machine learning</div>
<div class="feature-item">3. <span class="feature-label">Security Automation:</span> Ενισχυμένες αυτοματοποιημένες αποκρίσεις ασφαλείας</div>
<div class="feature-item">4. <span class="feature-label">Compliance Certification:</span> Επιδίωξη σχετικών πιστοποιήσεων ασφαλείας</div>

<div class="section-title">Συμπέρασμα</div>

Η εφαρμογή BioCleaning υλοποιεί ολοκληρωμένα μέτρα ασφαλείας enterprise επιπέδου που παρέχουν ισχυρή προστασία κατά των σύγχρονων cyber απειλών. Η αρχιτεκτονική ασφαλείας εξασφαλίζει ότι οι έλεγχοι ασφαλείας εφαρμόζονται κατάλληλα, παρέχοντας μέγιστη προστασία στην παραγωγή.

Όλες οι υλοποιήσεις ασφαλείας ακολουθούν τις βέλτιστες πρακτικές και πρότυπα της βιομηχανίας, παρέχοντας μια στέρεη βάση για ασφαλείς λειτουργίες και μελλοντικές βελτιώσεις ασφαλείας.

<div class="document-info">
<strong>Έκδοση Εγγράφου:</strong> 1.0<br>
<strong>Τελευταία Ενημέρωση:</strong> 2025-07-02<br>
<strong>Security Framework:</strong> Enterprise Security<br>
<strong>Συμμόρφωση:</strong> OWASP Top 10, NIST Cybersecurity Framework
</div>
