from typing import List, Optional
from pydantic import BaseModel, EmailStr


class CustomerBase(BaseModel):
    name: str
    info: str


class CustomerCreate(CustomerBase):
    pass


class CustomerUpdate(CustomerBase):
    pass


class Customer(CustomerBase):
    id: int
    user_email: str

    class Config:
        from_attributes = True


class UserSettingsBase(BaseModel):
    name: str
    info: str


class UserSettingsCreate(UserSettingsBase):
    email: EmailStr


class UserSettingsUpdate(UserSettingsBase):
    pass


class UserSettings(UserSettingsBase):
    email: str
    customers: List[Customer] = []

    class Config:
        from_attributes = True
