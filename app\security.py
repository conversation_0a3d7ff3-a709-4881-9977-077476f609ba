"""
Enterprise Security Management System
Environment-aware security features that provide production-grade security
while maintaining a smooth development experience.
"""

import os
import html
import hashlib
import secrets
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel
import re

from app.config import is_production, is_development, ENVIRONMENT

class SecurityConfig:
    """Environment-aware security configuration"""
    
    def __init__(self):
        self.environment = ENVIRONMENT
        self.is_production = is_production()
        self.is_development = is_development()
    
    @property
    def rate_limiting_enabled(self) -> bool:
        """Enable rate limiting only in production"""
        return self.is_production
    
    @property
    def csrf_protection_enabled(self) -> bool:
        """Enable CSRF protection only in production"""
        return self.is_production
    
    @property
    def strict_security_headers(self) -> bool:
        """Use strict security headers only in production"""
        return self.is_production
    
    @property
    def detailed_error_messages(self) -> bool:
        """Show detailed errors only in development"""
        return self.is_development
    
    @property
    def log_sensitive_data(self) -> bool:
        """Log sensitive data only in development"""
        return self.is_development
    
    def get_rate_limit(self, endpoint_type: str) -> str:
        """Get rate limit based on environment and endpoint type"""
        if not self.rate_limiting_enabled:
            return "1000/minute"  # Unlimited for development
        
        production_limits = {
            "auth": "5/minute",      # Login, signup, password reset
            "api": "100/minute",     # General API calls
            "email": "3/minute",     # Email sending endpoints
            "general": "50/minute"   # Default limit
        }
        return production_limits.get(endpoint_type, "50/minute")

# Global security configuration
security_config = SecurityConfig()

class InputSanitizer:
    """Comprehensive input sanitization and validation"""
    
    @staticmethod
    def sanitize_string(text: str, max_length: int = 1000) -> str:
        """Sanitize string input to prevent XSS and injection attacks"""
        if not isinstance(text, str):
            return ""
        
        # Truncate to max length
        text = text[:max_length]
        
        # Remove null bytes and control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # HTML escape to prevent XSS
        text = html.escape(text.strip())
        
        return text
    
    @staticmethod
    def sanitize_email(email: str) -> str:
        """Sanitize email input"""
        if not isinstance(email, str):
            return ""
        
        # Basic email sanitization
        email = email.strip().lower()
        
        # Remove dangerous characters
        email = re.sub(r'[<>"\'\x00-\x1f]', '', email)
        
        return email[:254]  # RFC 5321 limit
    
    @staticmethod
    def sanitize_form_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize all form data"""
        sanitized = {}
        
        for key, value in data.items():
            if isinstance(value, str):
                if 'email' in key.lower():
                    sanitized[key] = InputSanitizer.sanitize_email(value)
                elif 'password' in key.lower():
                    # Don't sanitize passwords, just validate length
                    sanitized[key] = value[:128]  # Reasonable password limit
                else:
                    sanitized[key] = InputSanitizer.sanitize_string(value)
            else:
                sanitized[key] = value
        
        return sanitized

class SecurityValidator:
    """Enhanced security validation"""
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, str]:
        """Validate password strength with detailed feedback"""
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"
        
        if len(password) > 128:
            return False, "Password is too long (max 128 characters)"
        
        # Check for required character types
        has_lower = bool(re.search(r'[a-z]', password))
        has_upper = bool(re.search(r'[A-Z]', password))
        has_digit = bool(re.search(r'\d', password))
        has_special = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password))
        
        if not (has_lower and has_upper and has_digit and has_special):
            return False, "Password must contain uppercase, lowercase, digit, and special character"
        
        # Check for common patterns
        common_patterns = [
            r'(.)\1{3,}',  # Repeated characters
            r'(012|123|234|345|456|567|678|789|890)',  # Sequential numbers
            r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)',  # Sequential letters
        ]
        
        for pattern in common_patterns:
            if re.search(pattern, password.lower()):
                return False, "Password contains predictable patterns"
        
        # Check against common passwords (basic check)
        common_passwords = [
            'password', '12345678', 'qwerty123', 'admin123', 'welcome123',
            'password123', 'letmein123', 'monkey123', '1234567890'
        ]
        
        if password.lower() in common_passwords:
            return False, "Password is too common"
        
        return True, "Password is strong"
    
    @staticmethod
    def validate_email_security(email: str) -> tuple[bool, str]:
        """Enhanced email validation for security"""
        # Basic format check
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return False, "Invalid email format"
        
        # Check for suspicious patterns
        if '..' in email or email.startswith('.') or email.endswith('.'):
            return False, "Invalid email format"
        
        # Check against disposable email domains (basic list)
        disposable_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
            'mailinator.com', 'yopmail.com', 'temp-mail.org'
        ]
        
        domain = email.split('@')[1].lower()
        if domain in disposable_domains:
            return False, "Disposable email addresses are not allowed"
        
        return True, "Email is valid"

class SecurityLogger:
    """Security event logging"""

    @staticmethod
    def log_auth_attempt(email: str, success: bool, ip_address: str, user_agent: str = ""):
        """Log authentication attempts"""
        # Import here to avoid circular imports
        from app.logging_config import security_logger, security_monitor

        event_type = "AUTH_SUCCESS" if success else "AUTH_FAILURE"

        log_data = {
            "event": event_type,
            "email": email if security_config.log_sensitive_data else "***",
            "ip": ip_address,
            "user_agent": user_agent[:200] if user_agent else "",
            "timestamp": datetime.utcnow().isoformat()
        }

        if success:
            security_logger.info(f"Authentication successful", extra=log_data)
        else:
            security_logger.warning(f"Authentication failed", extra=log_data)
            # Track failed attempts for monitoring
            security_monitor.track_failed_login(ip_address, email)

    @staticmethod
    def log_security_event(event_type: str, details: Dict[str, Any], ip_address: str = ""):
        """Log general security events"""
        from app.logging_config import security_logger

        log_data = {
            "event": event_type,
            "ip": ip_address,
            "timestamp": datetime.utcnow().isoformat(),
            **details
        }

        security_logger.info(f"Security event: {event_type}", extra=log_data)

    @staticmethod
    def log_rate_limit_exceeded(endpoint: str, ip_address: str):
        """Log rate limit violations"""
        from app.logging_config import security_monitor

        SecurityLogger.log_security_event(
            "RATE_LIMIT_EXCEEDED",
            {"endpoint": endpoint},
            ip_address
        )
        security_monitor.track_rate_limit_violation(ip_address, endpoint)

class CSRFProtection:
    """CSRF protection system"""
    
    @staticmethod
    def generate_csrf_token() -> str:
        """Generate a secure CSRF token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def validate_csrf_token(request: Request, provided_token: str) -> bool:
        """Validate CSRF token"""
        if not security_config.csrf_protection_enabled:
            return True  # Skip validation in development
        
        # In a real implementation, you'd store tokens in session/cache
        # For now, we'll implement a basic validation
        session_token = request.session.get("csrf_token")
        
        if not session_token or not provided_token:
            return False
        
        return secrets.compare_digest(session_token, provided_token)

# Export the main components
__all__ = [
    'security_config',
    'InputSanitizer', 
    'SecurityValidator',
    'SecurityLogger',
    'CSRFProtection'
]
