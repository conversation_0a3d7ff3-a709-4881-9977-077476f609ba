{# Example of how to include CSRF tokens in forms #}
{# This shows the pattern for adding CSRF protection to any form #}

<form method="post" action="/your-endpoint">
    {# CSRF token - only included in production #}
    {% if environment == "production" %}
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    {% endif %}
    
    {# Your form fields here #}
    <input type="email" name="email" required>
    <input type="password" name="password" required>
    
    <button type="submit">Submit</button>
</form>

{# 
Usage in your templates:
1. Add csrf_token to your template context
2. Add environment to your template context  
3. Include the hidden CSRF input as shown above
4. The security system will automatically validate the token
#}
