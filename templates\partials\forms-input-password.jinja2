<div data-signals="{_{{ namealwayschange }}isPasswordBOOL: true}" >
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 71px;
          padding-bottom: 20px;
      }

      me .input-group__error {
          color: var(--color-selected-red);
          display: block;
          position: relative;
          visibility: hidden;
          opacity: 0;
          margin-left: 10px;
          margin-top: 1px;
          margin-bottom: -44px;
          font-family: 'Noto Serif', serif;
          font-size: 14px;
          transition: all 0.3s ease-out;
      }

      me input {
          font-family: 'Noto Sans', sans-serif;
          font-size: 18px;
          color: var(--color-text-black);
          border: 0;
          z-index: 1;
          background-color: transparent;
          border-bottom: 1px solid var(--color-input-lines);
          padding: 0;

          &:focus {
              outline: 0;
              border-bottom: 1px solid var(--color-input-lines);

              &+.input-label {
                  font-family: 'Noto Serif', serif;
                  font-style: italic;
                  font-size: 14px;
                  color: var(--color-text-dark);
                  transform: translateY(-1.5rem);
              }
          }

          &:valid {
              border-bottom: 1px solid var(--color-selected-green);

              &+.input-label {
                  font-family: 'Noto Serif', serif;
                  font-style: italic;
                  font-size: 14px;
                  color: var(--color-text-dark);
                  transform: translateY(-1.5rem);
              }
          }

          &:not(:placeholder-shown):invalid {
              border-bottom: 1px solid var(--color-selected-red);
              animation: error-shake 600ms;

              &+.input-label {
                  font-family: 'Noto Serif', serif;
                  font-style: italic;
                  font-size: 14px;
                  color: var(--color-text-dark);
                  transform: translateY(-1.5rem);
              }
          }

          &:not(:placeholder-shown):not(:focus):invalid~.input-group__error {
              visibility: visible;
              opacity: 1;
          }
      }
  </style>

  <input data-attr-type="$_{{ namealwayschange }}isPasswordBOOL ? 'password' : 'text'"
         pattern="((?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W]).{8,64})"
         placeholder=""
         value=""
         name="{{ name | default(namealwayschange) }}"
         required />

  <label class="input-label" data-attr-disabled="$_{{ namealwayschange }}isPasswordBOOL">
    <style>
        me {
            color: var(--color-text-black);
            position: absolute;
            transition: .15s ease;
            margin-bottom: 6px;
        }
    </style>
    {{ label }}
  </label>

  <span class="input-group__error">Password invalid
    <button type="button" popovertarget="password-info-popover">
      <style>
          me {
              all: unset;
              background-color: transparent;
              color: var(--color-error-title);
              width: auto;
              height: auto;
              margin-left: 8px;
              text-align: center;
              cursor: pointer;
              transition: .25s ease;
          }

          me:hover {
              text-decoration: underline;
          }
      </style>
      More Info...
    </button>
  </span>

  <span class="fa-regular fa-eye" data-on-click="$_{{ namealwayschange }}isPasswordBOOL=!$_{{ namealwayschange }}isPasswordBOOL" data-class-fa-eye="$_{{ namealwayschange }}isPasswordBOOL" data-class-fa-eye-slash="!$_{{ namealwayschange }}isPasswordBOOL">
    <style>
        me {
            color: var(--color-input-lines);
            font-size: 18px;
            display: flex;
            margin-left: auto;
            margin-right: 6px;
            margin-bottom: 6px;
            z-index: 2;
        }
    </style>
  </span>

</div>
