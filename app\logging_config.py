"""
Security Logging Configuration
Comprehensive logging setup for security events and monitoring
"""

import logging
import logging.handlers
import os
from datetime import datetime
from typing import Dict, Any
import json

from app.config import is_production, ENVIRONMENT

class SecurityFormatter(logging.Formatter):
    """Custom formatter for security logs"""
    
    def format(self, record):
        # Create structured log entry
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "environment": ENVIRONMENT
        }
        
        # Add extra fields if present
        if hasattr(record, 'event'):
            log_entry["event"] = record.event
        if hasattr(record, 'ip'):
            log_entry["ip_address"] = record.ip
        if hasattr(record, 'user_agent'):
            log_entry["user_agent"] = record.user_agent
        if hasattr(record, 'email'):
            log_entry["email"] = record.email
        
        return json.dumps(log_entry)

def setup_security_logging():
    """Setup security logging configuration"""
    
    # Create security logger
    security_logger = logging.getLogger("security")
    security_logger.setLevel(logging.INFO)
    
    # Prevent duplicate logs
    if security_logger.handlers:
        return security_logger
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    if is_production():
        # Production logging - structured JSON logs
        handler = logging.handlers.RotatingFileHandler(
            "logs/security.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        handler.setFormatter(SecurityFormatter())
        
        # Also log to console in production for container logs
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(SecurityFormatter())
        
        security_logger.addHandler(handler)
        security_logger.addHandler(console_handler)
        
    else:
        # Development logging - human-readable format
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        security_logger.addHandler(handler)
    
    return security_logger

def setup_application_logging():
    """Setup general application logging"""
    
    # Create application logger
    app_logger = logging.getLogger("app")
    app_logger.setLevel(logging.INFO if is_production() else logging.DEBUG)
    
    # Prevent duplicate logs
    if app_logger.handlers:
        return app_logger
    
    os.makedirs("logs", exist_ok=True)
    
    if is_production():
        # Production - file logging with rotation
        handler = logging.handlers.RotatingFileHandler(
            "logs/application.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        app_logger.addHandler(handler)
        
    else:
        # Development - console logging
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        app_logger.addHandler(handler)
    
    return app_logger

# Initialize loggers
security_logger = setup_security_logging()
app_logger = setup_application_logging()

class SecurityMonitor:
    """Security monitoring and alerting"""
    
    def __init__(self):
        self.failed_login_attempts = {}
        self.suspicious_ips = set()
        self.rate_limit_violations = {}
    
    def track_failed_login(self, ip_address: str, email: str):
        """Track failed login attempts for monitoring"""
        if ip_address not in self.failed_login_attempts:
            self.failed_login_attempts[ip_address] = []
        
        self.failed_login_attempts[ip_address].append({
            "email": email,
            "timestamp": datetime.utcnow()
        })
        
        # Check for suspicious activity (5+ failed attempts in 10 minutes)
        recent_attempts = [
            attempt for attempt in self.failed_login_attempts[ip_address]
            if (datetime.utcnow() - attempt["timestamp"]).seconds < 600
        ]
        
        if len(recent_attempts) >= 5:
            self.mark_suspicious_ip(ip_address)
            security_logger.warning(
                f"Suspicious activity detected from IP {ip_address}: {len(recent_attempts)} failed login attempts",
                extra={"event": "SUSPICIOUS_ACTIVITY", "ip": ip_address}
            )
    
    def mark_suspicious_ip(self, ip_address: str):
        """Mark an IP as suspicious"""
        self.suspicious_ips.add(ip_address)
        security_logger.warning(
            f"IP {ip_address} marked as suspicious",
            extra={"event": "IP_MARKED_SUSPICIOUS", "ip": ip_address}
        )
    
    def is_suspicious_ip(self, ip_address: str) -> bool:
        """Check if an IP is marked as suspicious"""
        return ip_address in self.suspicious_ips
    
    def track_rate_limit_violation(self, ip_address: str, endpoint: str):
        """Track rate limit violations"""
        key = f"{ip_address}:{endpoint}"
        if key not in self.rate_limit_violations:
            self.rate_limit_violations[key] = 0
        
        self.rate_limit_violations[key] += 1
        
        security_logger.warning(
            f"Rate limit violation from {ip_address} on {endpoint}",
            extra={"event": "RATE_LIMIT_VIOLATION", "ip": ip_address, "endpoint": endpoint}
        )

# Global security monitor instance
security_monitor = SecurityMonitor()

# Export main components
__all__ = [
    'security_logger',
    'app_logger', 
    'security_monitor',
    'SecurityMonitor'
]
