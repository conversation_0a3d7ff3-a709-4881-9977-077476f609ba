from fastapi_mail import FastMail, MessageSchema, MessageType
from .mail_config import conf
from fastapi import UploadFile

async def send_reset_email(recipient: str, reset_url: str):
    html = f"""
    <html>
        <body>
            <p>(22)</p>
            <p>Hello,</p>
            <p>Click the link below to reset your password:</p>
            <p><a href="{reset_url}">Reset your password</a></p>
            <p>If you didn’t request a password reset, ignore this email.</p>
        </body>
    </html>
    """
    message = MessageSchema(
        subject="(21) Password Reset Request",
        recipients=[recipient],
        body=html,
        subtype=MessageType.html
    )
    fm = FastMail(conf)
    await fm.send_message(message)

async def send_report_email(recipient: str, upload_file: UploadFile, filename: str):
    html = f"""
    <html>
        <body>
            <p>(24)</p>
            <p>Hello,</p>
            <p>Please find attached your Calculation Report</p>
        </body>
    </html>
    """
    message = MessageSchema(
        subject="(23) Calculation Report",
        recipients=[recipient],
        body=html,
        subtype=MessageType.html,
        attachments=[upload_file]
    )
    fm = FastMail(conf)
    await fm.send_message(message)

async def send_authorization_email(recipient: str, verification_url: str):
    html = f"""
    <html>
        <body>
            <p>(19)</p>
            <p>Hello,</p>
            <p>Click the link below to authorize your email:</p>
            <p><a href="{verification_url}">Verify your email</a></p>
        </body>
    </html>
    """
    message = MessageSchema(
        subject="(18) Email Authorization",
        recipients=[recipient],
        body=html,
        subtype=MessageType.html
    )
    fm = FastMail(conf)
    await fm.send_message(message)
