from fpdf import FPDF
import os

def register_fonts_on_pdf(pdf: FPDF):
    font_path = os.path.join(os.path.dirname(__file__), "fonts")
    pdf.add_font("NotoSans100", "", os.path.join(font_path, "NotoSans-Thin.ttf"), uni=True)
    pdf.add_font("NotoSans100", "I", os.path.join(font_path, "NotoSans-ThinItalic.ttf"), uni=True)
    pdf.add_font("NotoSans200", "", os.path.join(font_path, "NotoSans-ExtraLight.ttf"), uni=True)
    pdf.add_font("NotoSans200", "I", os.path.join(font_path, "NotoSans-ExtraLightItalic.ttf"), uni=True)
    pdf.add_font("NotoSans300", "", os.path.join(font_path, "NotoSans-Light.ttf"), uni=True)
    pdf.add_font("NotoSans300", "I", os.path.join(font_path, "NotoSans-LightItalic.ttf"), uni=True)
    pdf.add_font("NotoSans400", "", os.path.join(font_path, "NotoSans-Regular.ttf"), uni=True)
    pdf.add_font("NotoSans400", "I", os.path.join(font_path, "NotoSans-Italic.ttf"), uni=True)
    pdf.add_font("NotoSans500", "", os.path.join(font_path, "NotoSans-Medium.ttf"), uni=True)
    pdf.add_font("NotoSans500", "I", os.path.join(font_path, "NotoSans-MediumItalic.ttf"), uni=True)
    pdf.add_font("NotoSans600", "", os.path.join(font_path, "NotoSans-SemiBold.ttf"), uni=True)
    pdf.add_font("NotoSans600", "I", os.path.join(font_path, "NotoSans-SemiBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSans700", "", os.path.join(font_path, "NotoSans-Bold.ttf"), uni=True)
    pdf.add_font("NotoSans700", "I", os.path.join(font_path, "NotoSans-BoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSans800", "", os.path.join(font_path, "NotoSans-ExtraBold.ttf"), uni=True)
    pdf.add_font("NotoSans800", "I", os.path.join(font_path, "NotoSans-ExtraBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSans900", "I", os.path.join(font_path, "NotoSans-BlackItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed100", "", os.path.join(font_path, "NotoSans-CondensedThin.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed100", "I", os.path.join(font_path, "NotoSans-CondensedThinItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed200", "", os.path.join(font_path, "NotoSans-CondensedExtraLight.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed200", "I", os.path.join(font_path, "NotoSans-CondensedExtraLightItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed300", "", os.path.join(font_path, "NotoSans-CondensedLight.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed300", "I", os.path.join(font_path, "NotoSans-CondensedLightItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed400", "", os.path.join(font_path, "NotoSans-Condensed.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed400", "I", os.path.join(font_path, "NotoSans-CondensedItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed500", "", os.path.join(font_path, "NotoSans-CondensedMedium.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed500", "I", os.path.join(font_path, "NotoSans-CondensedMediumItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed600", "", os.path.join(font_path, "NotoSans-CondensedSemiBold.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed600", "I", os.path.join(font_path, "NotoSans-CondensedSemiBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed700", "", os.path.join(font_path, "NotoSans-CondensedBold.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed700", "I", os.path.join(font_path, "NotoSans-CondensedBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed800", "", os.path.join(font_path, "NotoSans-CondensedExtraBold.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed800", "I", os.path.join(font_path, "NotoSans-CondensedExtraBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed900", "", os.path.join(font_path, "NotoSans-CondensedBlack.ttf"), uni=True)
    pdf.add_font("NotoSansCondensed900", "I", os.path.join(font_path, "NotoSans-CondensedBlackItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed100", "", os.path.join(font_path, "NotoSans-ExtraCondensedThin.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed100", "I", os.path.join(font_path, "NotoSans-ExtraCondensedThinItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed200", "", os.path.join(font_path, "NotoSans-ExtraCondensedExtraLight.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed200", "I", os.path.join(font_path, "NotoSans-ExtraCondensedExtraLightItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed300", "", os.path.join(font_path, "NotoSans-ExtraCondensedLight.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed300", "I", os.path.join(font_path, "NotoSans-ExtraCondensedLightItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed400", "", os.path.join(font_path, "NotoSans-ExtraCondensed.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed400", "I", os.path.join(font_path, "NotoSans-ExtraCondensedItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed500", "", os.path.join(font_path, "NotoSans-ExtraCondensedMedium.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed500", "I", os.path.join(font_path, "NotoSans-ExtraCondensedMediumItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed600", "", os.path.join(font_path, "NotoSans-ExtraCondensedSemiBold.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed600", "I", os.path.join(font_path, "NotoSans-ExtraCondensedSemiBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed700", "", os.path.join(font_path, "NotoSans-ExtraCondensedBold.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed700", "I", os.path.join(font_path, "NotoSans-ExtraCondensedBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed800", "", os.path.join(font_path, "NotoSans-ExtraCondensedExtraBold.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed800", "I", os.path.join(font_path, "NotoSans-ExtraCondensedExtraBoldItalic.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed900", "", os.path.join(font_path, "NotoSans-ExtraCondensedBlack.ttf"), uni=True)
    pdf.add_font("NotoSansExtraCondensed900", "I", os.path.join(font_path, "NotoSans-ExtraCondensedBlackItalic.ttf"), uni=True)

