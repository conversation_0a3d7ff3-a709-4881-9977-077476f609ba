{% block errorblock %}
  <div id="{{ id }}" data-signals="{_users_submit_button_disable:false}">
    <style>
        me {
            margin-top: 46px;
            margin-bottom: 26px;
            display: flex;
            flex-direction: column;
            width: auto;
            max-width: 820px;
            background-color: #ff00000b;
            border: 1px solid var(--color-background-middle);
            border-radius: 6px;
            padding-top: 26px;
            padding-bottom: 26px;
            padding-left: clamp(18px, 15%, 172px);
            padding-right: clamp(18px, 15%, 172px);
        }
    </style>
    <div>
      <style>
          me {
              width: 100%;
              text-align: center;
              padding-bottom: 8px;
          }

          me div {
              padding-bottom: 14px;
              font-family: 'Noto Serif', serif;
              font-size: 24px;
              font-weight: 400;
              font-stretch: semi-condensed;
              font-style: italic;
              color: var(--color-error-title);
          }

          me hr {
              border: none;
              border-bottom: 1px solid var(--color-hr-lines);
          }
      </style>
      <div>{{ errortitle }}</div>
      <hr />
    </div>
    <div>
      <style>
          me {
              width: 100%;
              text-align: center;
              padding-bottom: 8px;
              color: var(--color-selected-red);
          }
      </style>
      {{ signuperrormessage | safe }}
    </div>
  </div>
{% endblock errorblock %}

{% block signupsuccess %}
<div id="content-div">

  {# TITLE SINGLE #}
  <div>
    <style>
        me {
          width: 100%;
          text-align: center;
          padding-bottom: 8px;
        }

        me div {
          color: var(--color-text-title);
          padding-bottom: 14px;
          font-family: 'Noto Serif', serif;
          font-size: 24px;
          font-weight: 400;
          font-stretch: semi-condensed;
          font-style: italic;
        }

        me hr {
          display: block;
          height: 1px;
          border: 0;
          border-top: 1px solid var(--color-hr-lines);
          margin: 0;
          padding: 0;
        }
    </style>
    <div>Signup Successful!</div>
    <hr />
  </div>

  <div>
    <style>
        me {
            margin-top: 29px;
            margin-bottom: 0px;
            text-align: center;
        }

        me a {
            color: var(--color-text-dark);
            text-decoration: none;
            font-weight: 400;
            font-stretch: semi-condensed;
        }

        me a:hover {
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
    You can now
    <a href="/login_form">Log in</a>
  </div>

</div>
{% endblock signupsuccess %}

{% block success_and_redirect %}
<div id="content-div">

  {# TITLE SINGLE #}
  <div>
    <style>
        me {
          width: 100%;
          text-align: center;
          padding-bottom: 8px;
        }

        me div {
          color: var(--color-text-title);
          padding-bottom: 14px;
          font-family: 'Noto Serif', serif;
          font-size: 24px;
          font-weight: 400;
          font-stretch: semi-condensed;
          font-style: italic;
        }

        me hr {
          display: block;
          height: 1px;
          border: 0;
          border-top: 1px solid var(--color-hr-lines);
          margin: 0;
          padding: 0;
        }
    </style>
    <div>{{ title }}</div>
    <hr />
  </div>

  <div>
    <style>
        me {
            padding-bottom: 40px;
            padding-top: 40px;
        }

        .animated-points {
            display: inline-block;
            animation: blink 1s steps(4, end) infinite;
        }

        @keyframes blink {
            0%, 100% {
                opacity: 1;
            }
            25% {
                opacity: 0;
            }
        }
    </style>
    <span>{{ text }}</span>
    <span class="animated-points">.</span>
    <span class="animated-points">.</span>
    <span class="animated-points">.</span>
  </div>

</div>
{% endblock success_and_redirect %}

{% block resetpasswordsuccess %}
<div id="content-div">

  {# TITLE SINGLE #}
  <div>
    <style>
        me {
          width: 100%;
          text-align: center;
          padding-bottom: 8px;
        }

        me div {
          color: var(--color-text-title);
          padding-bottom: 14px;
          font-family: 'Noto Serif', serif;
          font-size: 24px;
          font-weight: 400;
          font-stretch: semi-condensed;
          font-style: italic;
        }

        me hr {
          display: block;
          height: 1px;
          border: 0;
          border-top: 1px solid var(--color-hr-lines);
          margin: 0;
          padding: 0;
        }
    </style>
    <div>Reset Password Successful!</div>
    <hr />
  </div>

  <div>
    <style>
        me {
            margin-top: 29px;
            margin-bottom: 0px;
            text-align: center;
        }

        me a {
            color: var(--color-text-dark);
            text-decoration: none;
            font-weight: 400;
            font-stretch: semi-condensed;
        }

        me a:hover {
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
    You can now
    <a href="/login_form">Log in</a>
    with your new password.
  </div>

</div>
{% endblock resetpasswordsuccess %}